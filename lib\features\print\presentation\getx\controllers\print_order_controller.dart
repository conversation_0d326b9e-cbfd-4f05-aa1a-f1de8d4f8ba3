import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/print/presentation/views/print_view.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';

class PrintOrderController extends GetxController {
  final ScreenshotController screenshotController = ScreenshotController();

  Future<Uint8List> _fetchLogoBytes(String url) async {
    try {
      final File file = await DefaultCacheManager().getSingleFile(url);
      final Uint8List bytes = await file.readAsBytes();
      return bytes;
    } catch (err) {
      throw Exception('Failed to load logo from network: $err');
    }
  }

  Future<void> printInvoice(PrintController controller) async {
    if (kIsWeb) {
      await _printInvoiceWeb(controller);
      return;
    }
    await _printInvoiceMobile(controller);
  }

  Future<void> _printInvoiceWeb(PrintController controller) async {
    try {
      Get.log('Printing invoice on web platform using image capture');
      await _printInvoiceAsImagePdf(controller);
      successSnackBar(
        'Invoice ready for printing. Please use your browser\'s print dialog.',
      );
    } catch (e) {
      Get.log('Web print error: $e');
      failedSnaskBar(
        'Failed to print invoice: $e',
      );
    }
  }

  Future<void> _printInvoiceAsImagePdf(PrintController controller) async {
    try {
      Get.log('Capturing invoice widget as image for PDF');
      final invoiceWidget = Invoice(controller: controller);

      final imageBytes = await _captureWidgetAsImage(invoiceWidget,
          targetWidthPixels:
              1066, // 4x larger width for PDF clarity (800 * 4/3)
          pixelRatio: 5.33); // 4x higher pixelRatio for PDF (4.0 * 4/3)

      final pdfBytes = await _createPdfFromImage(imageBytes);

      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfBytes,
        name: 'Invoice_${controller.orderNumber}',
        format: PdfPageFormat.a4,
        usePrinterSettings: true,
      );

      Get.log('Image-based PDF print dialog opened successfully');
    } catch (e) {
      Get.log('Image-based PDF printing error: $e');
      rethrow;
    }
  }

  /// Capture a Flutter widget as an image using screenshot package
  Future<Uint8List> _captureWidgetAsImage(
    Widget widget, {
    required int targetWidthPixels,
    double pixelRatio = 3.0,
  }) async {
    try {
      Get.log('Starting widget capture using screenshot package');

      final captureContextWidget = MaterialApp(
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          backgroundColor: Colors.white,
          body: SingleChildScrollView(
            // **Crucial: Use Builder to get a context for MediaQuery**
            child: Builder(
              builder: (context) {
                // Ensure the container fills the available width,
                // which should be `targetWidthPixels` as set by `screenshotController`.
                return Container(
                  width: targetWidthPixels.toDouble(),
                  // 4x larger padding for much bigger thermal printer output
                  padding: const EdgeInsets.symmetric(
                      horizontal: 64.0, // 4x larger horizontal padding (16 * 4)
                      vertical: 32.0), // 4x larger vertical padding (8 * 4)
                  color: Colors.white,
                  child: widget, // The actual invoice widget
                );
              },
            ),
          ),
        ),
      );

      final imageBytes = await screenshotController.captureFromWidget(
        captureContextWidget,
        delay: const Duration(milliseconds: 100),
        pixelRatio: pixelRatio,
        context: Get.context!,
      );

      Get.log(
          'Widget capture completed successfully - ${imageBytes.length} bytes');
      return imageBytes;
    } catch (e) {
      Get.log('Error capturing widget as image: $e');
      rethrow;
    }
  }

  Future<Uint8List> _createPdfFromImage(Uint8List imageBytes) async {
    final pdf = pw.Document();
    final image = pw.MemoryImage(imageBytes);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(10),
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Image(
              image,
              fit: pw.BoxFit.contain,
            ),
          );
        },
      ),
    );
    return pdf.save();
  }

  Future<void> _printToSinglePrinter(String printerIP, List<int> bytes) async {
    try {
      Get.log('Printing to $printerIP');
      const port = 9100;
      final printer = PrinterNetworkManager(printerIP, port: port);
      final result = await printer.printTicket(bytes);
      if (result == PosPrintResult.success) {
        Get.log('Successfully printed to $printerIP');
      } else {
        throw Exception('Failed to print to printer $printerIP: $result');
      }
    } catch (e) {
      Get.log('Error printing to $printerIP: $e');
      rethrow;
    }
  }

  Future<void> _printInvoiceMobile(PrintController controller) async {
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      Get.snackbar(
        'Error'.tr,
        'No printers configured. Please add printer IPs in settings.'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    Get.log(
        'Starting ESC/POS print job to ${printerIPs.length} printer(s) using image capture: $printerIPs');

    try {
      final invoiceWidget = Invoice(controller: controller);

      // 4x larger width for 80mm thermal printer (much bigger invoice)
      // 3328 pixels (832 * 4) provides much larger, more readable invoice
      final int printerMaxPixelWidth = 3328;

      final Uint8List capturedImageBytes = await _captureWidgetAsImage(
        invoiceWidget,
        targetWidthPixels: printerMaxPixelWidth,
        pixelRatio:
            8.0, // Much higher pixel ratio for very sharp text on thermal printers (6.0 * 4/3)
      );

      img.Image? originalImage = img.decodePng(capturedImageBytes);
      if (originalImage == null) {
        throw Exception("Failed to decode captured image for printing.");
      }

      final profile = await CapabilityProfile.load();
      final generator = Generator(PaperSize.mm80, profile);

      // Ensure the image is resized to fit the printer's maximum width.
      img.Image resizedImage =
          img.copyResize(originalImage, width: printerMaxPixelWidth);

      final List<int> escPosBytes = [];
      escPosBytes.addAll(generator.image(resizedImage));
      escPosBytes.addAll(generator.emptyLines(3));
      escPosBytes.addAll(generator.cut());

      Get.log(
          'Generated ${escPosBytes.length} bytes from image for thermal printer');

      final List<Future<void>> printTasks = [];
      for (String printerIP in printerIPs) {
        printTasks.add(_printToSinglePrinter(printerIP, escPosBytes));
      }

      try {
        await Future.wait(printTasks);
        Get.snackbar(
          'Success'.tr,
          'Invoice printed to ${printerIPs.length} printer(s) successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Get.theme.colorScheme.onPrimary,
        );
      } catch (e) {
        Get.snackbar(
          'Warning'.tr,
          'Some printers failed. Check printer connections.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
      }
    } catch (e) {
      Get.log('Mobile print error: $e');
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }
}
